import { ICalculation, ICalculationInput, ICalculationResult, IMaritimeCalculationInput, IMaritimeCalculationResult } from './model';
export declare class CalculationService {
    static calculateProforma(inputData: ICalculationInput[]): ICalculationResult;
    static createCalculation(userId: string, calculationData: {
        calculationType: 'proforma' | 'maliyet' | 'kar_zarar';
        inputData: ICalculationInput[];
        title?: string;
        description?: string;
        isPublic?: boolean;
        tags?: string[];
    }): Promise<ICalculation>;
    static getCalculationById(id: string, userId?: string): Promise<ICalculation | null>;
    static getUserCalculations(userId: string, filters?: {
        page?: number;
        limit?: number;
        calculationType?: string;
        search?: string;
        tags?: string;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        calculations: ICalculation[];
        totalCount: number;
        totalPages: number;
        currentPage: number;
    }>;
    static updateCalculation(id: string, userId: string, updateData: {
        title?: string;
        description?: string;
        isPublic?: boolean;
        tags?: string[];
    }): Promise<ICalculation | null>;
    static deleteCalculation(id: string, userId: string): Promise<boolean>;
    static duplicateCalculation(id: string, userId: string): Promise<ICalculation | null>;
    static getCalculationStats(userId: string): Promise<{
        totalCalculations: number;
        calculationsByType: {
            [key: string]: number;
        };
        totalNetAmount: number;
        averageNetAmount: number;
        lastCalculationDate: Date | null;
        mostUsedTags: string[];
    }>;
    static getPublicCalculations(filters?: {
        page?: number;
        limit?: number;
        calculationType?: string;
        search?: string;
        tags?: string;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        calculations: ICalculation[];
        totalCount: number;
        totalPages: number;
        currentPage: number;
    }>;
    private roundToNext1000;
    private validateInput;
    private calculatePilotageFee;
    private calculateTugboatFee;
    private calculateMooringFee;
    private calculateQuayDue;
    private calculateGarbageCollectionFee;
    private calculateSanitaryFee;
    private determineLightDueOperationType;
    private getAllLightDueOperations;
    private calculateLightDue;
    private calculateVesselTrafficFee;
    private calculateHarbourMasterFee;
    private calculateChamberOfShippingFee;
    private calculateAgencyFee;
    private calculateFreightShare;
    private calculateProgressiveRate;
    private calculateAttendanceSupervisionFee;
    private calculateContainerDangerousCargoFee;
    private calculateTransitPilotage;
    private calculateTransitTugboat;
    private calculateTransitOnly;
    calculateMaritimeProforma(userId: string, input: IMaritimeCalculationInput): Promise<IMaritimeCalculationResult>;
}
//# sourceMappingURL=service.d.ts.map