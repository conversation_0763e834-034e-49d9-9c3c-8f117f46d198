"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationController = void 0;
const service_1 = require("./service");
const validation_1 = require("./validation");
const exchangeRateService_1 = require("./exchangeRateService");
const pdfService_1 = require("../../services/pdfService");
class CalculationController {
    constructor() {
        this.calculateMaritimeProforma = async (req, res) => {
            try {
                const userId = req.user?._id;
                if (!userId) {
                    res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
                    return;
                }
                const { error, value } = (0, validation_1.validateMaritimeCalculationInput)(req.body);
                if (error) {
                    res.status(400).json({
                        error: 'Geçersiz giriş verileri',
                        details: error.details.map(d => d.message)
                    });
                    return;
                }
                const input = value;
                const result = await this.calculationService.calculateMaritimeProforma(userId, input);
                res.status(200).json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                console.error('Calculate maritime proforma error:', error);
                res.status(500).json({
                    error: 'Hesaplama sırasında hata oluştu',
                    details: error.message
                });
            }
        };
        this.calculateMaritimeProformaTest = async (req, res) => {
            try {
                const { error, value } = (0, validation_1.validateMaritimeCalculationInput)(req.body);
                if (error) {
                    res.status(400).json({
                        error: 'Geçersiz giriş verileri',
                        details: error.details.map(d => d.message)
                    });
                    return;
                }
                const input = value;
                const testUserId = 'test-user-id';
                const result = await this.calculationService.calculateMaritimeProforma(testUserId, input);
                res.status(200).json({
                    success: true,
                    data: result
                });
            }
            catch (error) {
                console.error('Calculate maritime proforma test error:', error);
                res.status(500).json({
                    error: 'Hesaplama sırasında hata oluştu',
                    details: error.message
                });
            }
        };
        this.getPortSuggestions = async (req, res) => {
            try {
                const { query } = req.query;
                const ports = [
                    { name: 'İzmir Demir Çelik (İDC)', location: 'Izmir' },
                    { name: 'Batıliman', location: 'Izmir' },
                    { name: 'Nemport', location: 'Izmir' },
                    { name: 'Ege Gübre', location: 'Izmir' },
                    { name: 'SOCAR', location: 'Izmir' },
                    { name: 'Tüpraş', location: 'Izmir' },
                    { name: 'STAR Rafineri', location: 'Izmir' },
                    { name: 'Petkim', location: 'Izmir' },
                    { name: 'Milangaz', location: 'Izmir' },
                    { name: 'Güzel Enerji', location: 'Izmir' },
                    { name: 'Ege Çelik', location: 'Izmir' },
                    { name: 'Batıçim', location: 'Izmir' },
                    { name: 'Egegaz', location: 'Izmir' },
                    { name: 'Alpet', location: 'Izmir' },
                    { name: 'Dikili Limanı', location: 'Izmir' },
                    { name: 'TCDD Alsancak', location: 'Izmir' },
                    { name: 'Nuh Çimento', location: 'Kocaeli' },
                    { name: 'Çolakoğlu', location: 'Kocaeli' },
                    { name: 'Ceyport', location: 'Kocaeli' },
                    { name: 'Martaş', location: 'Kocaeli' },
                    { name: 'Evyap', location: 'Kocaeli' },
                    { name: 'Limas', location: 'Kocaeli' },
                    { name: 'Solventas', location: 'Kocaeli' },
                    { name: 'Altınel', location: 'Kocaeli' },
                    { name: 'Poliport', location: 'Kocaeli' },
                    { name: 'Gebze Güzel Enerji', location: 'Kocaeli' },
                    { name: 'Tüpraş İzmit', location: 'Kocaeli' },
                    { name: 'Autoport', location: 'Kocaeli' },
                    { name: 'Aslan Çimento', location: 'Kocaeli' },
                    { name: 'Diler', location: 'Kocaeli' },
                    { name: 'Ford Otosan', location: 'Kocaeli' },
                    { name: 'Port Yarımca', location: 'Kocaeli' },
                    { name: 'Rota – Altınbaş', location: 'Kocaeli' },
                    { name: 'Asyaport', location: 'Tekirdağ' },
                    { name: 'DP World', location: 'Tekirdağ' },
                    { name: 'Yılport', location: 'Tekirdağ' },
                    { name: 'Beldeport', location: 'Tekirdağ' },
                    { name: 'Atakaş', location: 'Hatay' },
                    { name: 'Limak', location: 'Hatay' },
                    { name: 'Sönmez Çimento', location: 'Hatay' },
                    { name: 'Yazıcılar', location: 'Hatay' },
                    { name: 'Taros Tarım', location: 'Adana' },
                    { name: 'Çelebi Bandırma', location: 'Balıkesir' },
                    { name: 'Borusan Bandırma', location: 'Balıkesir' },
                    { name: 'Rodaport', location: 'Balıkesir' },
                    { name: 'İÇDAŞ', location: 'Bursa' },
                    { name: 'Port of Çanakkale', location: 'Canakkale' },
                    { name: 'Akçansa', location: 'Canakkale' },
                    { name: 'Kepez', location: 'Canakkale' },
                    { name: 'Erdemir', location: 'Zonguldak' },
                    { name: 'Filyos', location: 'Zonguldak' },
                    { name: 'TTK Zonguldak', location: 'Zonguldak' },
                    { name: 'OPET', location: 'Antalya' },
                    { name: 'MİP', location: 'Mersin' },
                    { name: 'ATAŞ', location: 'Mersin' },
                    { name: 'AVES', location: 'Mersin' },
                    { name: 'KUMPORT', location: 'Istanbul' },
                    { name: 'Haydarpaşa', location: 'Istanbul' },
                    { name: 'AKÇANSA Yalova', location: 'Yalova' },
                    { name: 'MARDAŞ', location: 'Yalova' },
                    { name: 'Zeytport', location: 'Bursa' },
                    { name: 'Gemport', location: 'Bursa' },
                    { name: 'Roda Port', location: 'Bursa' },
                    { name: 'Borusan Gemlik', location: 'Bursa' },
                    { name: 'Esun', location: 'Samsun' },
                    { name: 'Ceynak', location: 'Samsun' },
                    { name: 'Yeşilyurt', location: 'Samsun' },
                    { name: 'Samsunport', location: 'Samsun' },
                    { name: 'Toros', location: 'Trabzon' },
                    { name: 'Sadas', location: 'Trabzon' },
                    { name: 'Altınbaş', location: 'Artvin' },
                    { name: 'ETİ BAKIR', location: 'Giresun' }
                ];
                let filteredPorts = ports;
                if (query && typeof query === 'string') {
                    const searchTerm = query.toLowerCase();
                    filteredPorts = ports.filter(port => port.name.toLowerCase().includes(searchTerm) ||
                        port.location.toLowerCase().includes(searchTerm));
                }
                res.status(200).json({
                    success: true,
                    data: filteredPorts
                });
            }
            catch (error) {
                console.error('Get port suggestions error:', error);
                res.status(500).json({
                    error: 'Liman önerileri getirilirken hata oluştu',
                    details: error.message
                });
            }
        };
        this.getExchangeRates = async (req, res) => {
            try {
                const { date } = req.query;
                let rates;
                if (date && typeof date === 'string') {
                    rates = await exchangeRateService_1.ExchangeRateService.getRatesForDate(date);
                    if (!rates) {
                        res.status(404).json({
                            success: false,
                            error: 'Belirtilen tarih için kur bulunamadı'
                        });
                        return;
                    }
                    rates = { ...rates, source: 'TCMB' };
                }
                else {
                    rates = await exchangeRateService_1.ExchangeRateService.getCurrentRates();
                }
                res.status(200).json({
                    success: true,
                    data: {
                        usdTry: rates.usdTry,
                        eurUsd: rates.eurUsd,
                        date: rates.date,
                        source: rates.source,
                        lastUpdated: new Date().toISOString()
                    }
                });
            }
            catch (error) {
                console.error('Get exchange rates error:', error);
                const fallbackRates = {
                    usdTry: 30.0,
                    eurUsd: 1.1,
                    date: new Date().toISOString().split('T')[0],
                    source: 'FALLBACK',
                    lastUpdated: new Date().toISOString(),
                    warning: 'Bu kurlar gerçek veriler değildir. TCMB servisi kullanılamadığı için varsayılan değerler gösterilmektedir.'
                };
                res.status(200).json({
                    success: true,
                    data: fallbackRates,
                    warning: 'Exchange rate service temporarily unavailable, using fallback rates'
                });
            }
        };
        this.getVesselTypes = async (req, res) => {
            try {
                const vesselTypes = [
                    { value: 'passenger', label: 'Passenger' },
                    { value: 'ro-ro-ro-pax', label: 'Ro-Ro / Ro-Pax' },
                    { value: 'car-carrier', label: 'Car Carrier' },
                    { value: 'container', label: 'Container' },
                    { value: 'lng-tanker', label: 'LNG Tanker' },
                    { value: 'bunker-tanker', label: 'Bunker Tanker' },
                    { value: 'other', label: 'Other' }
                ];
                res.status(200).json({
                    success: true,
                    data: vesselTypes
                });
            }
            catch (error) {
                console.error('Get vessel types error:', error);
                res.status(500).json({
                    error: 'Gemi türleri getirilirken hata oluştu',
                    details: error.message
                });
            }
        };
        this.getCargoTypes = async (req, res) => {
            try {
                const cargoTypes = [
                    { value: 'general', label: 'General Cargo' },
                    { value: 'dangerous', label: 'Dangerous Cargo' }
                ];
                res.status(200).json({
                    success: true,
                    data: cargoTypes
                });
            }
            catch (error) {
                console.error('Get cargo types error:', error);
                res.status(500).json({
                    error: 'Kargo türleri getirilirken hata oluştu',
                    details: error.message
                });
            }
        };
        this.getPortCallTypes = async (req, res) => {
            try {
                const portCallTypes = [
                    { value: 'simple-port-call', label: 'Simple Port Call' },
                    { value: 'bosphorus-transit', label: 'Bosphorus Transit' },
                    { value: 'canakkale-transit', label: 'Çanakkale Transit' },
                    { value: 'transit-with-port-call', label: 'Transit with Port Call' }
                ];
                res.status(200).json({
                    success: true,
                    data: portCallTypes
                });
            }
            catch (error) {
                console.error('Get port call types error:', error);
                res.status(500).json({
                    error: 'Liman çağrısı türleri getirilirken hata oluştu',
                    details: error.message
                });
            }
        };
        this.getFlagCategories = async (req, res) => {
            try {
                const flagCategories = [
                    { value: 'cabotage', label: 'Cabotage' },
                    { value: 'turkish', label: 'Turkish' },
                    { value: 'foreign', label: 'Foreign' }
                ];
                res.status(200).json({
                    success: true,
                    data: flagCategories
                });
            }
            catch (error) {
                console.error('Get flag categories error:', error);
                res.status(500).json({
                    error: 'Bayrak kategorileri getirilirken hata oluştu',
                    details: error.message
                });
            }
        };
        this.generateCalculationPDF = async (req, res) => {
            try {
                const userId = req.user?._id;
                if (!userId) {
                    res.status(401).json({ error: 'Kullanıcı kimlik doğrulaması gerekli' });
                    return;
                }
                const { result, vesselName } = req.body;
                if (!result) {
                    res.status(400).json({ error: 'Hesaplama sonucu gerekli' });
                    return;
                }
                const pdfBuffer = await pdfService_1.PDFService.generateCalculationPDF(result, vesselName);
                const translatedPortName = this.translatePortNameForFilename(result.input.portName);
                const fileName = `Proforma_${translatedPortName}_${new Date().toISOString().split('T')[0]}.pdf`;
                res.setHeader('Content-Type', 'application/pdf');
                res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
                res.setHeader('Content-Length', pdfBuffer.length);
                res.send(pdfBuffer);
            }
            catch (error) {
                console.error('Generate PDF error:', error);
                res.status(500).json({
                    error: 'PDF oluşturulurken hata oluştu',
                    details: error.message
                });
            }
        };
        this.calculationService = new service_1.CalculationService();
    }
    static async createCalculation(req, res, next) {
        try {
            const { error } = validation_1.calculationValidationSchemas.createCalculation.validate(req.body);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const userId = req.user._id;
            const calculation = await service_1.CalculationService.createCalculation(userId, req.body);
            res.status(201).json({
                success: true,
                data: calculation,
                message: 'Hesaplama başarıyla oluşturuldu'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getCalculation(req, res, next) {
        try {
            const { id } = req.params;
            const userId = req.user?._id;
            const calculation = await service_1.CalculationService.getCalculationById(id, userId);
            if (!calculation) {
                res.status(404).json({
                    success: false,
                    error: 'Hesaplama bulunamadı'
                });
                return;
            }
            res.status(200).json({
                success: true,
                data: calculation
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getUserCalculations(req, res, next) {
        try {
            const { error } = validation_1.calculationValidationSchemas.getCalculations.validate(req.query);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const userId = req.user._id;
            const filters = req.query;
            const result = await service_1.CalculationService.getUserCalculations(userId, filters);
            res.status(200).json({
                success: true,
                data: result
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async updateCalculation(req, res, next) {
        try {
            const { error } = validation_1.calculationValidationSchemas.updateCalculation.validate(req.body);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const { id } = req.params;
            const userId = req.user._id;
            const calculation = await service_1.CalculationService.updateCalculation(id, userId, req.body);
            if (!calculation) {
                res.status(404).json({
                    success: false,
                    error: 'Hesaplama bulunamadı veya güncellenecek izniniz yok'
                });
                return;
            }
            res.status(200).json({
                success: true,
                data: calculation,
                message: 'Hesaplama başarıyla güncellendi'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async deleteCalculation(req, res, next) {
        try {
            const { id } = req.params;
            const userId = req.user._id;
            const success = await service_1.CalculationService.deleteCalculation(id, userId);
            if (!success) {
                res.status(404).json({
                    success: false,
                    error: 'Hesaplama bulunamadı veya silme izniniz yok'
                });
                return;
            }
            res.status(200).json({
                success: true,
                message: 'Hesaplama başarıyla silindi'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async duplicateCalculation(req, res, next) {
        try {
            const { id } = req.params;
            const userId = req.user._id;
            const duplicatedCalculation = await service_1.CalculationService.duplicateCalculation(id, userId);
            if (!duplicatedCalculation) {
                res.status(404).json({
                    success: false,
                    error: 'Kopyalanacak hesaplama bulunamadı'
                });
                return;
            }
            res.status(201).json({
                success: true,
                data: duplicatedCalculation,
                message: 'Hesaplama başarıyla kopyalandı'
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getCalculationStats(req, res, next) {
        try {
            const userId = req.user._id;
            const stats = await service_1.CalculationService.getCalculationStats(userId);
            res.status(200).json({
                success: true,
                data: stats
            });
        }
        catch (error) {
            next(error);
        }
    }
    static async getPublicCalculations(req, res, next) {
        try {
            const { error } = validation_1.calculationValidationSchemas.getCalculations.validate(req.query);
            if (error) {
                res.status(400).json({
                    success: false,
                    error: error.details[0].message
                });
                return;
            }
            const filters = req.query;
            const result = await service_1.CalculationService.getPublicCalculations(filters);
            res.status(200).json({
                success: true,
                data: result
            });
        }
        catch (error) {
            next(error);
        }
    }
    translatePortNameForFilename(portName) {
        return portName
            .replace(/ç/g, 'c')
            .replace(/Ç/g, 'C')
            .replace(/ğ/g, 'g')
            .replace(/Ğ/g, 'G')
            .replace(/ı/g, 'i')
            .replace(/İ/g, 'I')
            .replace(/ö/g, 'o')
            .replace(/Ö/g, 'O')
            .replace(/ş/g, 's')
            .replace(/Ş/g, 'S')
            .replace(/ü/g, 'u')
            .replace(/Ü/g, 'U')
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '_');
    }
}
exports.CalculationController = CalculationController;
CalculationController.getCalculations = CalculationController.getUserCalculations;
CalculationController.getCalculationById = CalculationController.getCalculation;
//# sourceMappingURL=controller.js.map