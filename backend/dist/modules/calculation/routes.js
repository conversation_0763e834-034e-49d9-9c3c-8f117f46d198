"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const controller_1 = require("./controller");
const auth_1 = require("../../middleware/auth");
const validation_1 = require("./validation");
const router = express_1.default.Router();
const calculationController = new controller_1.CalculationController();
router.post('/maritime/calculate', auth_1.auth, (0, validation_1.validateRequest)(validation_1.maritimeCalculationInputSchema), calculationController.calculateMaritimeProforma);
router.post('/maritime/test', (0, validation_1.validateRequest)(validation_1.maritimeCalculationInputSchema), calculationController.calculateMaritimeProformaTest);
router.get('/utils/ports', auth_1.auth, calculationController.getPortSuggestions);
router.get('/utils/exchange-rates', auth_1.auth, calculationController.getExchangeRates);
router.get('/utils/vessel-types', auth_1.auth, calculationController.getVesselTypes);
router.get('/utils/cargo-types', auth_1.auth, calculationController.getCargoTypes);
router.get('/utils/port-call-types', auth_1.auth, calculationController.getPortCallTypes);
router.get('/utils/flag-categories', auth_1.auth, calculationController.getFlagCategories);
router.post('/generate-pdf', auth_1.auth, calculationController.generateCalculationPDF);
router.post('/', auth_1.auth, controller_1.CalculationController.createCalculation);
router.get('/', auth_1.auth, controller_1.CalculationController.getCalculations);
router.get('/:id/legacy', auth_1.auth, controller_1.CalculationController.getCalculationById);
router.put('/:id', auth_1.auth, controller_1.CalculationController.updateCalculation);
router.delete('/:id/legacy', auth_1.auth, controller_1.CalculationController.deleteCalculation);
exports.default = router;
//# sourceMappingURL=routes.js.map