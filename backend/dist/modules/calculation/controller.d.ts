import { Request, Response, NextFunction } from 'express';
interface AuthRequest extends Request {
    user?: any;
}
export declare class CalculationController {
    private calculationService;
    constructor();
    static createCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getUserCalculations(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static updateCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static deleteCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static duplicateCalculation(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getCalculationStats(req: AuthRequest, res: Response, next: NextFunction): Promise<void>;
    static getPublicCalculations(req: Request, res: Response, next: NextFunction): Promise<void>;
    static getCalculations: typeof CalculationController.getUserCalculations;
    static getCalculationById: typeof CalculationController.getCalculation;
    calculateMaritimeProforma: (req: AuthRequest, res: Response) => Promise<void>;
    calculateMaritimeProformaTest: (req: Request, res: Response) => Promise<void>;
    getPortSuggestions: (req: AuthRequest, res: Response) => Promise<void>;
    getExchangeRates: (req: AuthRequest, res: Response) => Promise<void>;
    getVesselTypes: (req: AuthRequest, res: Response) => Promise<void>;
    getCargoTypes: (req: AuthRequest, res: Response) => Promise<void>;
    getPortCallTypes: (req: AuthRequest, res: Response) => Promise<void>;
    getFlagCategories: (req: AuthRequest, res: Response) => Promise<void>;
    generateCalculationPDF: (req: AuthRequest, res: Response) => Promise<void>;
    private translatePortNameForFilename;
}
export {};
//# sourceMappingURL=controller.d.ts.map