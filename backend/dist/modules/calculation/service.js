"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalculationService = void 0;
const model_1 = require("./model");
const mongoose_1 = __importDefault(require("mongoose"));
class CalculationService {
    static calculateProforma(inputData) {
        let toplamMaliyet = 0;
        let toplamKdvTutari = 0;
        let toplamIskontoTutari = 0;
        const detayliHesaplama = {
            kalemler: [],
            ozet: {}
        };
        inputData.forEach((item, index) => {
            const { maliyetGrubu, miktar, birimFiyat, kdvOrani, iskontoOrani = 0, ozelDurum, aciklama } = item;
            const araToplam = miktar * birimFiyat;
            const iskontoTutari = (araToplam * iskontoOrani) / 100;
            const iskontoSonrasiTutar = araToplam - iskontoTutari;
            const kdvTutari = (iskontoSonrasiTutar * kdvOrani) / 100;
            const netTutar = iskontoSonrasiTutar + kdvTutari;
            const kalemDetay = {
                siraNo: index + 1,
                maliyetGrubu,
                miktar,
                birimFiyat,
                araToplam,
                iskontoOrani,
                iskontoTutari,
                iskontoSonrasiTutar,
                kdvOrani,
                kdvTutari,
                netTutar,
                ozelDurum,
                aciklama
            };
            detayliHesaplama.kalemler.push(kalemDetay);
            toplamMaliyet += araToplam;
            toplamKdvTutari += kdvTutari;
            toplamIskontoTutari += iskontoTutari;
        });
        const netTutar = toplamMaliyet - toplamIskontoTutari + toplamKdvTutari;
        detayliHesaplama.ozet = {
            toplamKalem: inputData.length,
            toplamMaliyet,
            toplamIskontoTutari,
            iskontoSonrasiToplam: toplamMaliyet - toplamIskontoTutari,
            toplamKdvTutari,
            netTutar,
            ortalamaBirimFiyat: toplamMaliyet / inputData.reduce((sum, item) => sum + item.miktar, 0),
            ortalama_kdv_orani: toplamKdvTutari / (toplamMaliyet - toplamIskontoTutari) * 100
        };
        return {
            toplamMaliyet,
            kdvTutari: toplamKdvTutari,
            iskontoTutari: toplamIskontoTutari,
            netTutar,
            detayliHesaplama
        };
    }
    static async createCalculation(userId, calculationData) {
        let result;
        switch (calculationData.calculationType) {
            case 'proforma':
                result = this.calculateProforma(calculationData.inputData);
                break;
            case 'maliyet':
                result = this.calculateProforma(calculationData.inputData);
                break;
            case 'kar_zarar':
                result = this.calculateProforma(calculationData.inputData);
                break;
            default:
                throw new Error('Geçersiz hesaplama türü');
        }
        const calculation = await model_1.Calculation.create({
            userId,
            ...calculationData,
            result
        });
        return calculation;
    }
    static async getCalculationById(id, userId) {
        const query = { _id: id };
        if (userId) {
            query.$or = [
                { userId },
                { isPublic: true }
            ];
        }
        else {
            query.isPublic = true;
        }
        return await model_1.Calculation.findOne(query).populate('userId', 'name email');
    }
    static async getUserCalculations(userId, filters = {}) {
        const { page = 1, limit = 10, calculationType, search, tags, sortBy = 'calculationDate', sortOrder = 'desc' } = filters;
        const query = { userId };
        if (calculationType) {
            query.calculationType = calculationType;
        }
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }
        if (tags) {
            const tagArray = tags.split(',').map(tag => tag.trim());
            query.tags = { $in: tagArray };
        }
        const skip = (page - 1) * limit;
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const [calculations, totalCount] = await Promise.all([
            model_1.Calculation.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(limit)
                .populate('userId', 'name email'),
            model_1.Calculation.countDocuments(query)
        ]);
        return {
            calculations,
            totalCount,
            totalPages: Math.ceil(totalCount / limit),
            currentPage: page
        };
    }
    static async updateCalculation(id, userId, updateData) {
        return await model_1.Calculation.findOneAndUpdate({ _id: id, userId }, updateData, { new: true, runValidators: true });
    }
    static async deleteCalculation(id, userId) {
        const result = await model_1.Calculation.deleteOne({ _id: id, userId });
        return result.deletedCount > 0;
    }
    static async duplicateCalculation(id, userId) {
        const originalCalculation = await model_1.Calculation.findOne({ _id: id, userId });
        if (!originalCalculation) {
            return null;
        }
        const duplicatedCalculation = await model_1.Calculation.create({
            userId,
            calculationType: originalCalculation.calculationType,
            inputData: originalCalculation.inputData,
            result: originalCalculation.result,
            title: `${originalCalculation.title || 'Hesaplama'} (Kopya)`,
            description: originalCalculation.description,
            isPublic: false,
            tags: originalCalculation.tags
        });
        return duplicatedCalculation;
    }
    static async getCalculationStats(userId) {
        const [totalCalculations, calculationsByType, totalNetAmountResult, lastCalculation, tagsAggregation] = await Promise.all([
            model_1.Calculation.countDocuments({ userId }),
            model_1.Calculation.aggregate([
                { $match: { userId: new mongoose_1.default.Types.ObjectId(userId) } },
                { $group: { _id: '$calculationType', count: { $sum: 1 } } }
            ]),
            model_1.Calculation.aggregate([
                { $match: { userId: new mongoose_1.default.Types.ObjectId(userId) } },
                { $group: { _id: null, total: { $sum: '$result.netTutar' }, avg: { $avg: '$result.netTutar' } } }
            ]),
            model_1.Calculation.findOne({ userId }).sort({ calculationDate: -1 }),
            model_1.Calculation.aggregate([
                { $match: { userId: new mongoose_1.default.Types.ObjectId(userId) } },
                { $unwind: '$tags' },
                { $group: { _id: '$tags', count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 10 }
            ])
        ]);
        const calculationsByTypeObj = {};
        calculationsByType.forEach((item) => {
            calculationsByTypeObj[item._id] = item.count;
        });
        const totalNetAmount = totalNetAmountResult[0]?.total || 0;
        const averageNetAmount = totalNetAmountResult[0]?.avg || 0;
        const mostUsedTags = tagsAggregation.map((item) => item._id);
        return {
            totalCalculations,
            calculationsByType: calculationsByTypeObj,
            totalNetAmount,
            averageNetAmount,
            lastCalculationDate: lastCalculation?.calculationDate || null,
            mostUsedTags
        };
    }
    static async getPublicCalculations(filters = {}) {
        const { page = 1, limit = 10, calculationType, search, tags, sortBy = 'calculationDate', sortOrder = 'desc' } = filters;
        const query = { isPublic: true };
        if (calculationType) {
            query.calculationType = calculationType;
        }
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }
        if (tags) {
            const tagArray = tags.split(',').map(tag => tag.trim());
            query.tags = { $in: tagArray };
        }
        const skip = (page - 1) * limit;
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const [calculations, totalCount] = await Promise.all([
            model_1.Calculation.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(limit)
                .populate('userId', 'name'),
            model_1.Calculation.countDocuments(query)
        ]);
        return {
            calculations,
            totalCount,
            totalPages: Math.ceil(totalCount / limit),
            currentPage: page
        };
    }
    roundToNext1000(tonnage) {
        return Math.ceil(tonnage / 1000) * 1000;
    }
    validateInput(input) {
        const errors = [];
        if (!input.portName?.trim())
            errors.push('Liman adı gereklidir');
        if (!input.portLocation?.trim())
            errors.push('Liman lokasyonu gereklidir');
        if (!input.vesselType)
            errors.push('Gemi tipi gereklidir');
        if (!input.flagCategory)
            errors.push('Bayrak kategorisi gereklidir');
        if (!input.cargoType)
            errors.push('Yük tipi gereklidir');
        if (!input.portCallType)
            errors.push('Liman çağrısı tipi gereklidir');
        if (!input.grossRegisterTonnage || input.grossRegisterTonnage <= 0) {
            errors.push('Geçerli bir Gross Register Tonnage (GRT) değeri gereklidir');
        }
        if (!input.netRegisterTonnage || input.netRegisterTonnage <= 0) {
            errors.push('Geçerli bir Net Register Tonnage (NRT) değeri gereklidir');
        }
        if (!input.usdTryRate || input.usdTryRate <= 0) {
            errors.push('Geçerli bir USD/TRY kuru gereklidir');
        }
        if (input.portCallType === 'bosphorus-transit' || input.portCallType === 'canakkale-transit') {
            if (input.daysAtQuay > 0) {
                errors.push('Sadece geçiş için rıhtım günü sayısı 0 olmalıdır');
            }
        }
        else {
            if (!input.daysAtQuay || input.daysAtQuay <= 0) {
                errors.push('Liman çağrısı için rıhtım günü sayısı pozitif olmalıdır');
            }
        }
        if (input.deadweightTonnage && input.deadweightTonnage <= 0) {
            errors.push('Deadweight Tonnage (DWT) pozitif olmalıdır');
        }
        if (input.cargoQuantity && input.cargoQuantity < 0) {
            errors.push('Yük miktarı negatif olamaz');
        }
        if (input.eurUsdRate && input.eurUsdRate <= 0) {
            errors.push('EUR/USD kuru pozitif olmalıdır');
        }
        if (input.cargoQuantity && input.cargoQuantity > 0 && !input.cargoCategory) {
            errors.push('Yük miktarı belirtildiğinde yük kategorisi gereklidir');
        }
        return errors;
    }
    calculatePilotageFee(roundedGRT, vesselType, flagCategory, isIzmitKorfezi, isDangerous) {
        let baseRateFirstGT;
        let additionalRatePerGT;
        const numberOfPilots = 2;
        if (isIzmitKorfezi) {
            baseRateFirstGT = 152;
            additionalRatePerGT = 73;
        }
        else if (flagCategory === 'cabotage') {
            baseRateFirstGT = 70;
            additionalRatePerGT = 25;
        }
        else {
            switch (vesselType) {
                case 'passenger':
                case 'ro-ro-ro-pax':
                case 'car-carrier':
                    baseRateFirstGT = 116;
                    additionalRatePerGT = 46;
                    break;
                case 'container':
                    baseRateFirstGT = 153;
                    additionalRatePerGT = 65;
                    break;
                case 'lng-tanker':
                case 'bunker-tanker':
                case 'other':
                default:
                    baseRateFirstGT = 197;
                    additionalRatePerGT = 81;
                    break;
            }
        }
        const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
        const baseAmount = (baseRateFirstGT + (additionalGT * additionalRatePerGT)) * numberOfPilots;
        let total = isDangerous ? baseAmount * 1.30 : baseAmount;
        const dangerousSurcharge = isDangerous ? total - baseAmount : 0;
        let turkishFlagDiscount = 0;
        if (flagCategory === 'turkish') {
            turkishFlagDiscount = total * 0.20;
            total = total - turkishFlagDiscount;
        }
        return {
            baseAmount,
            numberOfPilots,
            dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
            turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
            total,
            calculation: `${roundedGRT / 1000} GT: First 1000 GT ${baseRateFirstGT} USD${additionalGT > 0 ? ` + ${additionalGT} x ${additionalRatePerGT} USD` : ''} x ${numberOfPilots} pilots${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}${flagCategory === 'turkish' ? ' - 20% Turkish flag discount' : ''}`
        };
    }
    calculateTugboatFee(roundedGRT, vesselType, flagCategory, isDangerous) {
        let numberOfTugs;
        if (roundedGRT <= 2000) {
            numberOfTugs = 2;
        }
        else if (roundedGRT <= 5000) {
            numberOfTugs = 4;
        }
        else if (roundedGRT <= 15000) {
            numberOfTugs = 6;
        }
        else if (roundedGRT <= 30000) {
            numberOfTugs = 4;
        }
        else {
            numberOfTugs = 6;
        }
        let baseRateFirstGT;
        let additionalRatePerGT;
        if (flagCategory === 'cabotage') {
            baseRateFirstGT = 119;
            additionalRatePerGT = 25;
        }
        else {
            switch (vesselType) {
                case 'passenger':
                case 'ro-ro-ro-pax':
                case 'car-carrier':
                    baseRateFirstGT = 224;
                    additionalRatePerGT = 40;
                    break;
                case 'container':
                    baseRateFirstGT = 299;
                    additionalRatePerGT = 56;
                    break;
                case 'lng-tanker':
                case 'bunker-tanker':
                case 'other':
                default:
                    baseRateFirstGT = 373;
                    additionalRatePerGT = 70;
                    break;
            }
        }
        const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
        const ratePerTug = baseRateFirstGT + (additionalGT * additionalRatePerGT);
        const baseAmount = ratePerTug * numberOfTugs;
        let total = isDangerous ? baseAmount * 1.30 : baseAmount;
        const dangerousSurcharge = isDangerous ? total - baseAmount : 0;
        let turkishFlagDiscount = 0;
        if (flagCategory === 'turkish') {
            turkishFlagDiscount = total * 0.20;
            total = total - turkishFlagDiscount;
        }
        return {
            numberOfTugs,
            ratePerTug,
            baseAmount,
            dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
            turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
            total,
            calculation: `${roundedGRT / 1000} GT: ${ratePerTug} USD/tugboat x ${numberOfTugs} tugboats${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}${flagCategory === 'turkish' ? ' - 20% Turkish flag discount' : ''}`
        };
    }
    calculateMooringFee(roundedGRT, flagCategory, vesselType, isDangerous) {
        let baseRateFirstGT;
        let additionalRatePerGT;
        if (flagCategory === 'cabotage') {
            baseRateFirstGT = 11;
            additionalRatePerGT = 6;
        }
        else {
            baseRateFirstGT = 22;
            additionalRatePerGT = 11;
        }
        const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
        const singleOperationRate = baseRateFirstGT + (additionalGT * additionalRatePerGT);
        const baseAmount = singleOperationRate * 2;
        let total = isDangerous ? baseAmount * 1.30 : baseAmount;
        const dangerousSurcharge = isDangerous ? total - baseAmount : 0;
        let turkishFlagDiscount = 0;
        if (flagCategory === 'turkish') {
            turkishFlagDiscount = total * 0.20;
            total = total - turkishFlagDiscount;
        }
        return {
            berthingFee: singleOperationRate,
            unberthingFee: singleOperationRate,
            baseAmount,
            dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
            turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
            total,
            calculation: `${roundedGRT / 1000} GT: ${singleOperationRate} USD x 2 (berthing + unberthing)${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}${flagCategory === 'turkish' ? ' - 20% Turkish flag discount' : ''}`
        };
    }
    calculateQuayDue(roundedGRT, flagCategory, vesselType, daysAtQuay, isDangerous) {
        let baseRateFirstGT;
        let additionalRatePerGT;
        switch (flagCategory) {
            case 'cabotage':
                baseRateFirstGT = 13;
                additionalRatePerGT = 8;
                break;
            case 'turkish':
                baseRateFirstGT = 19;
                additionalRatePerGT = 19;
                break;
            case 'foreign':
                baseRateFirstGT = 25;
                additionalRatePerGT = 25;
                break;
        }
        const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
        const dailyRate = baseRateFirstGT + (additionalGT * additionalRatePerGT);
        const baseAmount = dailyRate * daysAtQuay;
        let total = isDangerous ? baseAmount * 1.30 : baseAmount;
        const dangerousSurcharge = isDangerous ? total - baseAmount : 0;
        let turkishFlagDiscount = 0;
        if (flagCategory === 'turkish') {
            turkishFlagDiscount = total * 0.25;
            total = total - turkishFlagDiscount;
        }
        let calculation = `${roundedGRT / 1000} GT: ${dailyRate} USD/day x ${daysAtQuay} days`;
        if (isDangerous) {
            calculation += ' x 1.30 (dangerous cargo surcharge)';
        }
        if (flagCategory === 'turkish') {
            calculation += ' - 25% (Turkish flag discount)';
        }
        return {
            dailyRate,
            days: daysAtQuay,
            baseAmount,
            dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
            turkishFlagDiscount: flagCategory === 'turkish' ? turkishFlagDiscount : undefined,
            total,
            calculation
        };
    }
    calculateGarbageCollectionFee(roundedGRT, eurUsdRate) {
        let amountEUR;
        if (roundedGRT <= 1000) {
            amountEUR = 80;
        }
        else if (roundedGRT >= 1001 && roundedGRT <= 5000) {
            amountEUR = 140;
        }
        else if (roundedGRT >= 5001 && roundedGRT <= 10000) {
            amountEUR = 210;
        }
        else if (roundedGRT >= 10001 && roundedGRT <= 15000) {
            amountEUR = 250;
        }
        else if (roundedGRT >= 15001 && roundedGRT <= 20000) {
            amountEUR = 300;
        }
        else if (roundedGRT >= 20001 && roundedGRT <= 25000) {
            amountEUR = 350;
        }
        else if (roundedGRT >= 25001 && roundedGRT <= 35000) {
            amountEUR = 400;
        }
        else if (roundedGRT >= 35001 && roundedGRT <= 60000) {
            amountEUR = 540;
        }
        else {
            amountEUR = 720;
        }
        const usdAmount = eurUsdRate ? amountEUR * eurUsdRate : amountEUR;
        return {
            amount: amountEUR,
            currency: 'EUR',
            usdAmount,
            calculation: `${roundedGRT} GT: ${amountEUR} EUR${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
        };
    }
    calculateSanitaryFee(nrt, flagCategory, usdTryRate, isTransitOnly) {
        let baseAmountTL;
        if (isTransitOnly) {
            baseAmountTL = nrt * 0.3803;
        }
        else {
            switch (flagCategory) {
                case 'foreign':
                    baseAmountTL = nrt * 17.27;
                    break;
                case 'cabotage':
                case 'turkish':
                    if (nrt >= 50 && nrt <= 250) {
                        baseAmountTL = nrt * 13.73;
                    }
                    else if (nrt > 250) {
                        baseAmountTL = nrt * 31.00;
                    }
                    else {
                        baseAmountTL = nrt * 13.73;
                    }
                    break;
            }
        }
        const usdAmount = baseAmountTL / usdTryRate;
        return {
            amount: usdAmount,
            baseAmountTL,
            usdAmount,
            calculation: `${nrt} NRT x ${baseAmountTL / nrt} TL = ${baseAmountTL.toFixed(2)} TL (${usdAmount.toFixed(2)} USD)`
        };
    }
    determineLightDueOperationType(portCallType, flagCategory, isTransit) {
        if (flagCategory !== 'turkish') {
            return 'standard';
        }
        if (isTransit) {
            switch (portCallType) {
                case 'bosphorus-transit':
                    return 'bosphorus-entry';
                case 'canakkale-transit':
                    return 'dardanelles-entry';
                default:
                    return 'standard';
            }
        }
        else {
            return 'port-entry';
        }
    }
    getAllLightDueOperations(portCallType, flagCategory) {
        if (flagCategory !== 'turkish') {
            return ['standard'];
        }
        switch (portCallType) {
            case 'simple-port-call':
                return ['port-entry', 'port-exit'];
            case 'bosphorus-transit':
                return ['bosphorus-entry', 'bosphorus-exit'];
            case 'canakkale-transit':
                return ['dardanelles-entry', 'dardanelles-exit'];
            case 'transit-with-port-call':
                return ['bosphorus-entry', 'bosphorus-exit', 'port-entry', 'port-exit'];
            default:
                return ['standard'];
        }
    }
    calculateLightDue(nrt, flagCategory, operationType, vesselTrafficFee) {
        let first800NRT;
        let above800NRT;
        let multiplierUsed;
        let operationDescription;
        const finalOperationType = operationType || 'standard';
        if (flagCategory === 'turkish' && finalOperationType !== 'standard') {
            let highMultiplier;
            let lowMultiplier;
            switch (finalOperationType) {
                case 'round-trip-without-port-call':
                    highMultiplier = 2.1294;
                    lowMultiplier = 1.0647;
                    operationDescription = 'Uğrakısız Sefer (Gidiş-Dönüş)';
                    break;
                case 'dardanelles-entry':
                    highMultiplier = 0.19008;
                    lowMultiplier = 0.09504;
                    operationDescription = 'Çanakkale Boğazı Giriş';
                    break;
                case 'dardanelles-exit':
                    highMultiplier = 0.19008;
                    lowMultiplier = 0.09504;
                    operationDescription = 'Çanakkale Boğazı Çıkış';
                    break;
                case 'bosphorus-entry':
                    highMultiplier = 0.19008;
                    lowMultiplier = 0.09504;
                    operationDescription = 'İstanbul Boğazı Giriş';
                    break;
                case 'bosphorus-exit':
                    highMultiplier = 0.19008;
                    lowMultiplier = 0.09504;
                    operationDescription = 'İstanbul Boğazı Çıkış';
                    break;
                case 'port-entry':
                    highMultiplier = 0.118272;
                    lowMultiplier = 0.059136;
                    operationDescription = 'Liman Giriş';
                    break;
                case 'port-exit':
                    highMultiplier = 0.118272;
                    lowMultiplier = 0.059136;
                    operationDescription = 'Liman Çıkış';
                    break;
                default:
                    highMultiplier = 0.2112;
                    lowMultiplier = 0.1056;
                    operationDescription = 'Standard';
            }
            if (nrt <= 800) {
                first800NRT = nrt * highMultiplier;
                above800NRT = 0;
                multiplierUsed = highMultiplier;
            }
            else {
                first800NRT = 800 * highMultiplier;
                above800NRT = (nrt - 800) * lowMultiplier;
                multiplierUsed = highMultiplier;
            }
        }
        else {
            first800NRT = Math.min(nrt, 800) * 0.2112;
            above800NRT = Math.max(0, nrt - 800) * 0.1056;
            multiplierUsed = 0.2112;
            operationDescription = 'Standard';
        }
        const lightDueTotal = first800NRT + above800NRT;
        const total = lightDueTotal + (vesselTrafficFee || 0);
        let calculation;
        if (flagCategory === 'turkish' && finalOperationType !== 'standard') {
            if (nrt <= 800) {
                calculation = `${nrt} NRT x ${multiplierUsed} (${operationDescription}): ${first800NRT.toFixed(2)} USD`;
            }
            else {
                calculation = `800 NRT x ${multiplierUsed} + ${nrt - 800} NRT x ${above800NRT > 0 ? (above800NRT / (nrt - 800)).toFixed(5) : '0'} (${operationDescription}): ${lightDueTotal.toFixed(2)} USD`;
            }
        }
        else {
            calculation = `First 800 NRT: ${first800NRT.toFixed(2)} USD + ${Math.max(0, nrt - 800)} NRT x 0.1056: ${above800NRT.toFixed(2)} USD`;
        }
        if (vesselTrafficFee) {
            calculation += ` + Vessel traffic fee: ${vesselTrafficFee} USD`;
        }
        return {
            first800NRT,
            above800NRT,
            vesselTrafficFee,
            operationType: finalOperationType,
            multiplierUsed,
            total,
            calculation
        };
    }
    calculateVesselTrafficFee(portLocation, nrt, flagCategory, vesselType) {
        const applicablePorts = [
            'izmit', 'kocaeli', 'izmir', 'mersin',
            'İzmit', 'Kocaeli', 'İzmir', 'Mersin'
        ];
        const portLocationLower = portLocation.toLowerCase();
        const isApplicable = applicablePorts.some(port => portLocationLower.includes(port.toLowerCase()));
        if (!isApplicable) {
            console.log(`Vessel Traffic Fee not applicable for port location: ${portLocation}`);
            return undefined;
        }
        let feeAmount;
        let rangeDescription;
        if (nrt >= 300 && nrt <= 2000) {
            rangeDescription = '300-2,000 Net Ton';
            if (flagCategory === 'cabotage') {
                feeAmount = 8;
            }
            else if (flagCategory === 'turkish') {
                feeAmount = 22;
            }
            else {
                if (vesselType === 'passenger') {
                    feeAmount = 70.4;
                }
                else {
                    feeAmount = 88;
                }
            }
        }
        else if (nrt >= 2001 && nrt <= 5000) {
            rangeDescription = '2,001-5,000 Net Ton';
            if (flagCategory === 'cabotage') {
                feeAmount = 16;
            }
            else if (flagCategory === 'turkish') {
                feeAmount = 44;
            }
            else {
                if (vesselType === 'passenger') {
                    feeAmount = 140.8;
                }
                else {
                    feeAmount = 176;
                }
            }
        }
        else if (nrt >= 5001 && nrt <= 10000) {
            rangeDescription = '5,001-10,000 Net Ton';
            if (flagCategory === 'cabotage') {
                feeAmount = 30;
            }
            else if (flagCategory === 'turkish') {
                feeAmount = 82.5;
            }
            else {
                if (vesselType === 'passenger') {
                    feeAmount = 264;
                }
                else {
                    feeAmount = 330;
                }
            }
        }
        else if (nrt >= 10001 && nrt <= 20000) {
            rangeDescription = '10,001-20,000 Net Ton';
            if (flagCategory === 'cabotage') {
                feeAmount = 45;
            }
            else if (flagCategory === 'turkish') {
                feeAmount = 123.75;
            }
            else {
                if (vesselType === 'passenger') {
                    feeAmount = 396;
                }
                else {
                    feeAmount = 495;
                }
            }
        }
        else if (nrt >= 20001 && nrt <= 50000) {
            rangeDescription = '20,001-50,000 Net Ton';
            if (flagCategory === 'cabotage') {
                feeAmount = 60;
            }
            else if (flagCategory === 'turkish') {
                feeAmount = 165;
            }
            else {
                if (vesselType === 'passenger') {
                    feeAmount = 528;
                }
                else {
                    feeAmount = 660;
                }
            }
        }
        else if (nrt >= 50001) {
            rangeDescription = '50,001+ Net Ton';
            if (flagCategory === 'cabotage') {
                feeAmount = 90;
            }
            else if (flagCategory === 'turkish') {
                feeAmount = 247.5;
            }
            else {
                if (vesselType === 'passenger') {
                    feeAmount = 792;
                }
                else {
                    feeAmount = 990;
                }
            }
        }
        else {
            console.log(`Vessel Traffic Fee not applicable for NRT below 300: ${nrt}`);
            return undefined;
        }
        let flagDescription;
        if (flagCategory === 'cabotage') {
            flagDescription = 'Turkish Cabotage';
        }
        else if (flagCategory === 'turkish') {
            flagDescription = 'Turkish Non-Cabotage';
        }
        else {
            flagDescription = vesselType === 'passenger' ? 'Foreign Passenger' : 'Foreign Commercial';
        }
        console.log(`Vessel Traffic Fee applied for port location: ${portLocation}, NRT: ${nrt}, Flag: ${flagDescription}`);
        return {
            entry: feeAmount,
            departure: feeAmount,
            total: feeAmount * 2,
            nrtRange: rangeDescription,
            flagCategory: flagDescription,
            calculation: `${nrt} NRT (${rangeDescription}, ${flagDescription}): ${feeAmount} USD x 2 = ${feeAmount * 2} USD`,
            applicablePorts: applicablePorts.filter(port => portLocationLower.includes(port.toLowerCase()))
        };
    }
    calculateHarbourMasterFee(nrt, usdTryRate, flagCategory) {
        if (flagCategory === 'cabotage') {
            return {
                baseAmountTL: 0,
                usdAmount: 0,
                calculation: 'Not applicable for cabotage flagged vessels'
            };
        }
        let baseAmountTL;
        if (nrt <= 500) {
            baseAmountTL = 1079.40;
        }
        else if (nrt <= 2000) {
            baseAmountTL = 2878.60;
        }
        else if (nrt <= 4000) {
            baseAmountTL = 5757.20;
        }
        else if (nrt <= 8000) {
            baseAmountTL = 8635.80;
        }
        else if (nrt <= 10000) {
            baseAmountTL = 14393.00;
        }
        else if (nrt <= 30000) {
            baseAmountTL = 28786.00;
        }
        else if (nrt <= 50000) {
            baseAmountTL = 43179.00;
        }
        else {
            baseAmountTL = 71965.00;
        }
        const usdAmount = baseAmountTL / usdTryRate;
        return {
            baseAmountTL,
            usdAmount,
            calculation: `${nrt} NRT: ${baseAmountTL} TL (${usdAmount.toFixed(2)} USD)`
        };
    }
    calculateChamberOfShippingFee(roundedGRT, flagCategory, vesselType, usdTryRate) {
        let amountTL;
        if (flagCategory === 'foreign') {
            if (roundedGRT <= 500) {
                if (vesselType === 'passenger') {
                    amountTL = 1200;
                }
                else {
                    amountTL = 1400;
                }
            }
            else if (roundedGRT <= 1500) {
                if (vesselType === 'passenger') {
                    amountTL = 2400;
                }
                else {
                    amountTL = 2800;
                }
            }
            else if (roundedGRT <= 2500) {
                if (vesselType === 'passenger') {
                    amountTL = 3600;
                }
                else {
                    amountTL = 4200;
                }
            }
            else if (roundedGRT <= 5000) {
                if (vesselType === 'passenger') {
                    amountTL = 4200;
                }
                else {
                    amountTL = 4900;
                }
            }
            else if (roundedGRT <= 10000) {
                if (vesselType === 'passenger') {
                    amountTL = 4800;
                }
                else {
                    amountTL = 5600;
                }
            }
            else if (roundedGRT <= 25000) {
                amountTL = 6300;
            }
            else if (roundedGRT <= 35000) {
                amountTL = 7000;
            }
            else if (roundedGRT <= 50000) {
                amountTL = 7500;
            }
            else {
                amountTL = 8000;
            }
        }
        else {
            if (roundedGRT <= 500) {
                if (vesselType === 'passenger') {
                    amountTL = 600;
                }
                else {
                    amountTL = 670;
                }
            }
            else if (roundedGRT <= 1500) {
                if (vesselType === 'passenger') {
                    amountTL = 950;
                }
                else {
                    amountTL = 1120;
                }
            }
            else if (roundedGRT <= 2500) {
                if (vesselType === 'passenger') {
                    amountTL = 1750;
                }
                else {
                    amountTL = 2050;
                }
            }
            else if (roundedGRT <= 5000) {
                if (vesselType === 'passenger') {
                    amountTL = 2400;
                }
                else {
                    amountTL = 2800;
                }
            }
            else if (roundedGRT <= 10000) {
                if (vesselType === 'passenger') {
                    amountTL = 2900;
                }
                else {
                    amountTL = 3400;
                }
            }
            else if (roundedGRT <= 25000) {
                amountTL = 4000;
            }
            else if (roundedGRT <= 35000) {
                amountTL = 4500;
            }
            else if (roundedGRT <= 50000) {
                amountTL = 5000;
            }
            else {
                amountTL = 5300;
            }
        }
        const usdAmount = amountTL / usdTryRate;
        const flagDescription = flagCategory === 'foreign' ? 'Foreign Flag (Figure 009)' : 'Turkish Flag (Figure 008)';
        const vesselTypeDescription = vesselType === 'passenger' ? 'Passenger/Cruise' : 'Other Ships';
        return {
            amount: amountTL,
            baseAmountTL: amountTL,
            currency: 'TL',
            usdAmount,
            calculation: `${roundedGRT} GT (${flagDescription}, ${vesselTypeDescription}): ${amountTL.toFixed(2)} TL (${usdAmount.toFixed(2)} USD)`
        };
    }
    calculateAgencyFee(nrt, eurUsdRate, isTransit) {
        let amountEUR;
        let calculationDetails;
        if (isTransit) {
            if (nrt <= 1000) {
                amountEUR = 200;
            }
            else if (nrt <= 2000) {
                amountEUR = 290;
            }
            else if (nrt <= 3000) {
                amountEUR = 340;
            }
            else if (nrt <= 4000) {
                amountEUR = 400;
            }
            else if (nrt <= 5000) {
                amountEUR = 460;
            }
            else if (nrt <= 7500) {
                amountEUR = 560;
            }
            else if (nrt <= 10000) {
                amountEUR = 640;
            }
            else {
                let baseAmount = 640;
                let additionalAmount = 0;
                if (nrt > 10000) {
                    const excessNRT = nrt - 10000;
                    if (nrt <= 20000) {
                        const additionalThousands = Math.ceil(excessNRT / 1000);
                        additionalAmount += additionalThousands * 30;
                    }
                    else if (nrt <= 30000) {
                        additionalAmount += 10 * 30;
                        const excessAbove20k = nrt - 20000;
                        const additionalThousands = Math.ceil(excessAbove20k / 1000);
                        additionalAmount += additionalThousands * 20;
                    }
                    else {
                        additionalAmount += 10 * 30;
                        additionalAmount += 10 * 20;
                        const excessAbove30k = nrt - 30000;
                        const additionalThousands = Math.ceil(excessAbove30k / 1000);
                        additionalAmount += additionalThousands * 10;
                    }
                }
                amountEUR = baseAmount + additionalAmount;
            }
            calculationDetails = `${nrt} NRT (Transit): ${amountEUR} EUR`;
        }
        else {
            if (nrt <= 500) {
                amountEUR = 660;
            }
            else if (nrt <= 1000) {
                amountEUR = 1000;
            }
            else if (nrt <= 2000) {
                amountEUR = 1500;
            }
            else if (nrt <= 3000) {
                amountEUR = 1850;
            }
            else if (nrt <= 4000) {
                amountEUR = 2300;
            }
            else if (nrt <= 5000) {
                amountEUR = 2750;
            }
            else if (nrt <= 7500) {
                amountEUR = 3200;
            }
            else if (nrt <= 10000) {
                amountEUR = 4000;
            }
            else {
                let baseAmount = 4000;
                let additionalAmount = 0;
                if (nrt > 10000) {
                    const excessNRT = nrt - 10000;
                    if (nrt <= 20000) {
                        const additionalThousands = Math.ceil(excessNRT / 1000);
                        additionalAmount += additionalThousands * 125;
                    }
                    else if (nrt <= 30000) {
                        additionalAmount += 10 * 125;
                        const excessAbove20k = nrt - 20000;
                        const additionalThousands = Math.ceil(excessAbove20k / 1000);
                        additionalAmount += additionalThousands * 100;
                    }
                    else {
                        additionalAmount += 10 * 125;
                        additionalAmount += 10 * 100;
                        const excessAbove30k = nrt - 30000;
                        const additionalThousands = Math.ceil(excessAbove30k / 1000);
                        additionalAmount += additionalThousands * 75;
                    }
                }
                amountEUR = baseAmount + additionalAmount;
            }
            calculationDetails = `${nrt} NRT (Port): ${amountEUR} EUR`;
        }
        const usdAmount = eurUsdRate ? amountEUR * eurUsdRate : amountEUR;
        return {
            amount: amountEUR,
            currency: 'EUR',
            usdAmount,
            calculation: `${calculationDetails}${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
        };
    }
    calculateFreightShare(cargoCategory, tonnageOrUnits, vesselType) {
        let currency = 'USD';
        let freightShareAmount;
        let rangeDescription;
        if (vesselType === 'ro-ro-ro-pax' || vesselType === 'container') {
            freightShareAmount = 700;
            rangeDescription = vesselType === 'ro-ro-ro-pax' ? 'Ro-Ro vessel fixed rate' : 'Container vessel fixed rate';
            return {
                cargoCategory,
                tonnageOrUnits,
                ratePerUnit: 0,
                baseAmount: freightShareAmount,
                finalAmount: freightShareAmount,
                currency,
                usdAmount: freightShareAmount,
                calculation: `${vesselType === 'ro-ro-ro-pax' ? 'Ro-Ro' : 'Container'} vessel: ${freightShareAmount} USD (fixed rate)`
            };
        }
        if (tonnageOrUnits <= 20000) {
            freightShareAmount = 650;
            rangeDescription = '0-20,000 ton';
        }
        else if (tonnageOrUnits <= 40000) {
            freightShareAmount = 1000;
            rangeDescription = '20,001-40,000 ton';
        }
        else if (tonnageOrUnits <= 60000) {
            freightShareAmount = 1300;
            rangeDescription = '40,001-60,000 ton';
        }
        else if (tonnageOrUnits <= 100000) {
            freightShareAmount = 1600;
            rangeDescription = '60,001-100,000 ton';
        }
        else {
            freightShareAmount = 2000;
            rangeDescription = '100,001+ ton';
        }
        return {
            cargoCategory,
            tonnageOrUnits,
            ratePerUnit: 0,
            baseAmount: freightShareAmount,
            finalAmount: freightShareAmount,
            currency,
            usdAmount: freightShareAmount,
            calculation: `${tonnageOrUnits} ton (${rangeDescription}): ${freightShareAmount} USD`
        };
    }
    calculateProgressiveRate(tonnage, tiers) {
        let totalAmount = 0;
        let remainingTonnage = tonnage;
        let breakdown = '';
        for (let i = 0; i < tiers.length; i++) {
            const tier = tiers[i];
            const isLastTier = i === tiers.length - 1;
            if (remainingTonnage <= 0)
                break;
            let tierTonnage;
            if (isLastTier) {
                tierTonnage = remainingTonnage;
            }
            else {
                const previousLimit = i > 0 ? tiers[i - 1].limit : 0;
                const tierCapacity = tier.limit - previousLimit;
                tierTonnage = Math.min(remainingTonnage, tierCapacity);
            }
            const tierAmount = tierTonnage * tier.rate;
            totalAmount += tierAmount;
            if (breakdown)
                breakdown += ' + ';
            if (isLastTier && remainingTonnage > 0) {
                breakdown += `${tierTonnage} ton x ${tier.rate} EUR`;
            }
            else {
                const rangeStart = i > 0 ? tiers[i - 1].limit + 1 : 0;
                const rangeEnd = tier.limit;
                breakdown += `${tierTonnage} ton (${rangeStart}-${rangeEnd}) x ${tier.rate} EUR`;
            }
            remainingTonnage -= tierTonnage;
        }
        return { amount: totalAmount, breakdown };
    }
    calculateAttendanceSupervisionFee(cargoCategory, tonnageOrUnits, eurUsdRate) {
        let baseAmount;
        let currency = 'EUR';
        let categoryDescription;
        let calculationBreakdown;
        switch (cargoCategory?.toLowerCase()) {
            case 'bulk-solid-minerals':
                const solidMineralsTiers = [
                    { limit: 10000, rate: 0.15 },
                    { limit: 20000, rate: 0.10 },
                    { limit: Infinity, rate: 0.05 }
                ];
                const solidMineralsResult = this.calculateProgressiveRate(tonnageOrUnits, solidMineralsTiers);
                baseAmount = solidMineralsResult.amount;
                calculationBreakdown = solidMineralsResult.breakdown;
                categoryDescription = 'A-a) Katı Eşya (Maden cevheri, mineraller, hurda, pik demiri, kömür, keçi boynuzu, hayvan yemi, küspe, çimento, kilinker, ponza, suni gübre, mucur)';
                break;
            case 'bulk-grains':
                const grainsTiers = [
                    { limit: 10000, rate: 0.10 },
                    { limit: 25000, rate: 0.075 },
                    { limit: Infinity, rate: 0.045 }
                ];
                const grainsResult = this.calculateProgressiveRate(tonnageOrUnits, grainsTiers);
                baseAmount = grainsResult.amount;
                calculationBreakdown = grainsResult.breakdown;
                categoryDescription = 'A-b) Tahıl ve Tohumlar (Buğday, arpa, yulaf, çavdar, pirinç, mısır, ayçiçeği, soya fasulyesi, fığ)';
                break;
            case 'bulk-legumes':
                const legumesTiers = [
                    { limit: 5000, rate: 0.30 },
                    { limit: Infinity, rate: 0.15 }
                ];
                const legumesResult = this.calculateProgressiveRate(tonnageOrUnits, legumesTiers);
                baseAmount = legumesResult.amount;
                calculationBreakdown = legumesResult.breakdown;
                categoryDescription = 'A-c) Bakliyat (Bakla, börülce, fasulye, mercimek, nohut)';
                break;
            case 'bulk-petroleum':
                const petroleumTiers = [
                    { limit: 15000, rate: 0.040 },
                    { limit: 35000, rate: 0.030 },
                    { limit: Infinity, rate: 0.015 }
                ];
                const petroleumResult = this.calculateProgressiveRate(tonnageOrUnits, petroleumTiers);
                baseAmount = petroleumResult.amount;
                calculationBreakdown = petroleumResult.breakdown;
                categoryDescription = 'A-d) Ham petrol, akaryakıt';
                break;
            case 'bulk-gas':
                const gasTiers = [
                    { limit: 15000, rate: 0.15 },
                    { limit: Infinity, rate: 0.05 }
                ];
                const gasResult = this.calculateProgressiveRate(tonnageOrUnits, gasTiers);
                baseAmount = gasResult.amount;
                calculationBreakdown = gasResult.breakdown;
                categoryDescription = 'A-e) LPG ve LNG Gazlar';
                break;
            case 'bulk-chemicals':
                baseAmount = tonnageOrUnits * 0.15;
                calculationBreakdown = `${tonnageOrUnits} ton x 0.15 EUR`;
                categoryDescription = 'A-f) Kimyevi maddeler (Petrol ürünü olanlar dahil) Şarap, zeytinyağı, melas, yemeklik sıvı yağ, madeni yağ, donyağı';
                break;
            case 'non-bulk-grains':
                const nonBulkGrainsTiers = [
                    { limit: 20000, rate: 0.15 },
                    { limit: Infinity, rate: 0.05 }
                ];
                const nonBulkGrainsResult = this.calculateProgressiveRate(tonnageOrUnits, nonBulkGrainsTiers);
                baseAmount = nonBulkGrainsResult.amount;
                calculationBreakdown = nonBulkGrainsResult.breakdown;
                categoryDescription = 'B-a) Tahıl ve un, suni gübre, şeker, çimento, pirinç, irmik, keçi boynuzu, mineraller, mermer blok';
                break;
            case 'fresh-produce':
                baseAmount = tonnageOrUnits * 1.00;
                calculationBreakdown = `${tonnageOrUnits} ton x 1.00 EUR`;
                categoryDescription = 'B-b) Taze meyve ve sebze, narenciye, dondurulmuş gıda';
                break;
            case 'non-bulk-legumes':
                baseAmount = tonnageOrUnits * 0.60;
                calculationBreakdown = `${tonnageOrUnits} ton x 0.60 EUR`;
                categoryDescription = 'B-c) Bakliyat ve Tohumlar';
                break;
            case 'steel-paper':
                const steelPaperTiers = [
                    { limit: 5000, rate: 0.25 },
                    { limit: 10000, rate: 0.15 },
                    { limit: Infinity, rate: 0.10 }
                ];
                const steelPaperResult = this.calculateProgressiveRate(tonnageOrUnits, steelPaperTiers);
                baseAmount = steelPaperResult.amount;
                calculationBreakdown = steelPaperResult.breakdown;
                categoryDescription = 'B-d) Kâğıt ve Demir çelik mamulü ile yarı mamulleri (Saç levha, kangal demir, profil, kütük demir, inşaat demiri, firkete demir, filmaşin, her türlü boru, rulo saç, gazete kağıdı, rulo kağıt, kağıt hamuru)';
                break;
            case 'wood-logs':
                const woodLogsTiers = [
                    { limit: 3000, rate: 0.50 },
                    { limit: 5000, rate: 0.35 },
                    { limit: Infinity, rate: 0.10 }
                ];
                const woodLogsResult = this.calculateProgressiveRate(tonnageOrUnits, woodLogsTiers);
                baseAmount = woodLogsResult.amount;
                calculationBreakdown = woodLogsResult.breakdown;
                categoryDescription = 'B-e) Ağaç kütüğü ve tomruk';
                break;
            case 'empty-containers':
                baseAmount = tonnageOrUnits * 10.00;
                calculationBreakdown = `${tonnageOrUnits} adet x 10.00 EUR`;
                categoryDescription = 'C) Boş Konteyner ve Boş Treyler (Euro/Adet) - Yıllık 25.000 adet boş konteyner üzerinde işlem yapan hatlar bu ücretten muaftır';
                break;
            case 'livestock-small':
                baseAmount = tonnageOrUnits * 0.05;
                calculationBreakdown = `${tonnageOrUnits} adet x 0.05 EUR`;
                categoryDescription = 'D-a) Küçükbaş (Euro/Adet)';
                break;
            case 'livestock-large':
                baseAmount = tonnageOrUnits * 0.15;
                calculationBreakdown = `${tonnageOrUnits} adet x 0.15 EUR`;
                categoryDescription = 'D-b) Büyükbaş (Euro/Adet)';
                break;
            case 'other-cargo-carrier-pays':
                baseAmount = tonnageOrUnits * 1.00;
                calculationBreakdown = `${tonnageOrUnits} ton x 1.00 EUR`;
                categoryDescription = 'E-I) Diğer Eşya, Kırkambar Olarak Taşınan Yükler - Yükleme ve/veya boşaltma ücretlerinin taşıyan tarafından ödenmesi halinde';
                break;
            case 'other-cargo-shipper-pays':
                baseAmount = tonnageOrUnits * 0.60;
                calculationBreakdown = `${tonnageOrUnits} ton x 0.60 EUR`;
                categoryDescription = 'E-II) Diğer Eşya, Kırkambar Olarak Taşınan Yükler - Yükleme ve/veya boşaltma ücretlerinin yükleyici ve/veya alıcı tarafından ödenmesi halinde';
                break;
            case 'containers':
                baseAmount = tonnageOrUnits * 15.00;
                calculationBreakdown = `${tonnageOrUnits} adet x 15.00 EUR`;
                categoryDescription = 'F) Konteyner İçinde Taşınan Tüm Yükler (Yukarıdakiler dâhil) euro/adet';
                break;
            case 'transit-containers':
                baseAmount = tonnageOrUnits * 15.00;
                calculationBreakdown = `${tonnageOrUnits} adet x 15.00 EUR`;
                categoryDescription = 'F) Gemi ile yurt dışından gelip başka gemi ile yurt dışına giden transit konteyner (euro/adet) - Yıllık 50.000 adet dolu konteyner üzerinde işlem yapan hatlar bu ücretten muaftır';
                break;
            case 'vehicles':
                const vehiclesTiers = [
                    { limit: 50, rate: 5.00 },
                    { limit: 300, rate: 3.00 },
                    { limit: Infinity, rate: 1.50 }
                ];
                const vehiclesResult = this.calculateProgressiveRate(tonnageOrUnits, vehiclesTiers);
                baseAmount = vehiclesResult.amount;
                calculationBreakdown = vehiclesResult.breakdown.replace(/ton/g, 'adet');
                categoryDescription = 'G) Otomobil, Jeep, Pikap, Panelvan, Minibüs, Midibüs EURO/Beher Adet - Yükleme ve/veya boşaltma ücretlerinin taşıyan, yükleyici ve/veya alıcı tarafından ödenmesi halinde';
                break;
            case 'ro-ro-vehicles':
                baseAmount = tonnageOrUnits * 3.00;
                calculationBreakdown = `${tonnageOrUnits} metre x 3.00 EUR`;
                categoryDescription = 'H) RO-RO Gemilerle Taşınan ve Kendi Kendine Yürüyebilen Tekerlekli Paletli Vasıta ve İş Makinaları EURO/Beher Metre - Euro/Uzunluğu üzerinden beher metre için';
                break;
            default:
                baseAmount = tonnageOrUnits * 0.60;
                calculationBreakdown = `${tonnageOrUnits} ton x 0.60 EUR`;
                categoryDescription = 'Genel yük';
                break;
        }
        const minAmount = 300;
        const maxAmount = 10000;
        const finalAmount = Math.max(minAmount, Math.min(maxAmount, baseAmount));
        const usdAmount = eurUsdRate ? finalAmount * eurUsdRate : finalAmount;
        let unitType = 'ton';
        if (['containers', 'transit-containers', 'vehicles', 'empty-containers', 'livestock-small', 'livestock-large'].includes(cargoCategory || '')) {
            unitType = 'adet';
        }
        else if (cargoCategory === 'ro-ro-vehicles') {
            unitType = 'metre';
        }
        return {
            cargoCategory: categoryDescription,
            tonnageOrUnits,
            ratePerUnit: 0,
            baseAmount,
            minAmount,
            maxAmount,
            finalAmount,
            currency,
            usdAmount,
            calculation: `${calculationBreakdown} = ${baseAmount.toFixed(2)} EUR (min: ${minAmount}, max: ${maxAmount}) = ${finalAmount.toFixed(2)} EUR${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
        };
    }
    calculateContainerDangerousCargoFee(teuCount, eurUsdRate) {
        const ratePerTEU = 25.00;
        const currency = 'EUR';
        const totalAmount = teuCount * ratePerTEU;
        const usdAmount = eurUsdRate ? totalAmount * eurUsdRate : totalAmount;
        return {
            teuCount,
            ratePerTEU,
            totalAmount,
            currency,
            usdAmount,
            calculation: `${teuCount} TEU x ${ratePerTEU} EUR/TEU = ${totalAmount.toFixed(2)} EUR${eurUsdRate ? ` (${usdAmount.toFixed(2)} USD)` : ''}`
        };
    }
    calculateTransitPilotage(roundedGRT, vesselType, isDangerous) {
        const baseRateFirstGT = 550;
        const additionalRatePerGT = 100;
        const numberOfTransits = 2;
        const additionalGT = Math.max(0, (roundedGRT / 1000) - 1);
        const baseAmount = (baseRateFirstGT + (additionalGT * additionalRatePerGT)) * numberOfTransits;
        const total = isDangerous ? baseAmount * 1.30 : baseAmount;
        const dangerousSurcharge = isDangerous ? total - baseAmount : 0;
        return {
            baseAmount,
            numberOfTransits,
            dangerousSurcharge: isDangerous ? dangerousSurcharge : undefined,
            total,
            calculation: `${roundedGRT / 1000} GT: First 1000 GT ${baseRateFirstGT} USD${additionalGT > 0 ? ` + ${additionalGT} x ${additionalRatePerGT} USD` : ''} x ${numberOfTransits} pilots${isDangerous ? ' x 1.30 (dangerous cargo surcharge)' : ''}`
        };
    }
    calculateTransitTugboat(roundedGRT, vesselType, vesselLength, isDangerous) {
        const requiresTugboat = ((vesselLength >= 200 && vesselLength <= 300 &&
            ['ro-ro-ro-pax', 'container', 'lng-tanker', 'bunker-tanker', 'other'].includes(vesselType)) ||
            isDangerous);
        if (!requiresTugboat)
            return undefined;
        return this.calculateTugboatFee(roundedGRT, vesselType, 'foreign', isDangerous);
    }
    calculateTransitOnly(input, roundedGRT) {
        const isDangerous = Boolean(input.cargoType === 'dangerous' || input.isDangerous);
        const transitPilotage = this.calculateTransitPilotage(roundedGRT, input.vesselType, isDangerous);
        const transitSanitary = this.calculateSanitaryFee(input.netRegisterTonnage, input.flagCategory, input.usdTryRate, true);
        const transitOperationType = this.determineLightDueOperationType(input.portCallType, input.flagCategory, true);
        const transitLight = this.calculateLightDue(input.netRegisterTonnage, input.flagCategory, transitOperationType);
        const transitAgency = this.calculateAgencyFee(input.netRegisterTonnage, input.eurUsdRate, true);
        let transitTugboat;
        if (input.deadweightTonnage) {
            const estimatedLength = Math.min(300, Math.max(150, input.deadweightTonnage / 100));
            transitTugboat = this.calculateTransitTugboat(roundedGRT, input.vesselType, estimatedLength, isDangerous);
        }
        const transitTotal = transitPilotage.total +
            (transitTugboat?.total || 0) +
            transitSanitary.usdAmount +
            transitLight.total +
            transitAgency.usdAmount;
        return {
            transitPilotage: transitPilotage.total,
            transitTugboat: transitTugboat?.total || 0,
            transitSanitary: transitSanitary.usdAmount,
            transitLight: transitLight.total,
            transitAgency: transitAgency.usdAmount,
            total: transitTotal
        };
    }
    async calculateMaritimeProforma(userId, input) {
        const validationErrors = this.validateInput(input);
        if (validationErrors.length > 0) {
            throw new Error(`Geçersiz giriş verileri: ${validationErrors.join(', ')}`);
        }
        const roundedGRT = this.roundToNext1000(input.grossRegisterTonnage);
        const roundedNRT = this.roundToNext1000(input.netRegisterTonnage);
        const isDangerous = Boolean(input.cargoType === 'dangerous' || input.isDangerous);
        const isIzmitKorfezi = Boolean(input.isIzmitKorfezi ||
            input.portLocation.toLowerCase().includes('izmit') ||
            input.portLocation.toLowerCase().includes('kocaeli') ||
            input.portName.toLowerCase().includes('izmit'));
        const calculationNotes = [];
        if (roundedGRT !== input.grossRegisterTonnage) {
            calculationNotes.push(`GRT rounded from ${input.grossRegisterTonnage} to ${roundedGRT}`);
        }
        if (roundedNRT !== input.netRegisterTonnage) {
            calculationNotes.push(`NRT rounded from ${input.netRegisterTonnage} to ${roundedNRT}`);
        }
        if (isDangerous) {
            calculationNotes.push('Dangerous cargo surcharge of 30% applied');
        }
        if (isIzmitKorfezi) {
            calculationNotes.push('İzmit Körfezi Etap pilotaj tarifesi uygulandı');
        }
        if (input.portCallType === 'bosphorus-transit' || input.portCallType === 'canakkale-transit') {
            const transitFees = this.calculateTransitOnly(input, roundedGRT);
            const fees = {
                pilotage: { baseAmount: 0, numberOfPilots: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                tugboat: { numberOfTugs: 0, ratePerTug: 0, baseAmount: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                mooring: { berthingFee: 0, unberthingFee: 0, baseAmount: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                quayDue: { dailyRate: 0, days: 0, baseAmount: 0, total: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                garbageCollection: { amount: 0, currency: 'EUR', usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                sanitaryFee: this.calculateSanitaryFee(input.netRegisterTonnage, input.flagCategory, input.usdTryRate, true),
                lightDue: this.calculateLightDue(input.netRegisterTonnage, input.flagCategory, 'standard'),
                harbourMasterFee: { baseAmountTL: 0, usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                chamberOfShipping: { amount: 0, currency: 'EUR', usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                agencyFee: this.calculateAgencyFee(input.netRegisterTonnage, input.eurUsdRate, true),
                attendanceSupervisionFee: { cargoCategory: 'geçiş', tonnageOrUnits: 0, ratePerUnit: 0, baseAmount: 0, minAmount: 0, maxAmount: 0, finalAmount: 0, currency: 'EUR', usdAmount: 0, calculation: 'Sadece geçiş - uygulanmaz' },
                transitFees
            };
            return {
                input,
                roundedGRT,
                roundedNRT,
                fees,
                subtotals: {
                    portCallServices: 0,
                    transitServices: transitFees.total
                },
                dangerousCargoSurcharge: isDangerous,
                grandTotal: transitFees.total,
                currency: 'USD',
                calculationNotes
            };
        }
        const pilotage = this.calculatePilotageFee(roundedGRT, input.vesselType, input.flagCategory, isIzmitKorfezi, isDangerous);
        const tugboat = this.calculateTugboatFee(roundedGRT, input.vesselType, input.flagCategory, isDangerous);
        const mooring = this.calculateMooringFee(roundedGRT, input.flagCategory, input.vesselType, isDangerous);
        const quayDue = this.calculateQuayDue(roundedGRT, input.flagCategory, input.vesselType, input.daysAtQuay, isDangerous);
        const garbageCollection = this.calculateGarbageCollectionFee(roundedGRT, input.eurUsdRate);
        const sanitaryFee = this.calculateSanitaryFee(input.netRegisterTonnage, input.flagCategory, input.usdTryRate);
        const vesselTrafficFee = this.calculateVesselTrafficFee(input.portLocation, input.netRegisterTonnage, input.flagCategory, input.vesselType);
        const portOperationType = this.determineLightDueOperationType(input.portCallType, input.flagCategory, false);
        const lightDue = this.calculateLightDue(input.netRegisterTonnage, input.flagCategory, portOperationType, vesselTrafficFee?.total);
        const harbourMasterFee = this.calculateHarbourMasterFee(input.netRegisterTonnage, input.usdTryRate, input.flagCategory);
        const chamberOfShipping = this.calculateChamberOfShippingFee(roundedGRT, input.flagCategory, input.vesselType, input.usdTryRate);
        const agencyFee = this.calculateAgencyFee(input.netRegisterTonnage, input.eurUsdRate, false);
        let freightShare;
        let attendanceSupervisionFee;
        if (input.cargoQuantity && input.cargoCategory) {
            freightShare = this.calculateFreightShare(input.cargoCategory, input.cargoQuantity, input.vesselType);
            attendanceSupervisionFee = this.calculateAttendanceSupervisionFee(input.cargoCategory, input.cargoQuantity, input.eurUsdRate);
        }
        let transitFees;
        if (input.portCallType === 'transit-with-port-call') {
            transitFees = this.calculateTransitOnly(input, roundedGRT);
        }
        const fees = {
            pilotage,
            tugboat,
            mooring,
            quayDue,
            garbageCollection,
            sanitaryFee,
            lightDue,
            vesselTrafficFee,
            harbourMasterFee,
            chamberOfShipping,
            freightShare: freightShare || {
                amount: 0,
                currency: 'USD',
                usdAmount: 0,
                calculation: 'Yük bilgisi eksik'
            },
            agencyFee,
            attendanceSupervisionFee: attendanceSupervisionFee || {
                cargoCategory: input.cargoCategory || 'genel',
                tonnageOrUnits: input.cargoQuantity || 0,
                ratePerUnit: 0,
                baseAmount: 0,
                minAmount: 300,
                maxAmount: 10000,
                finalAmount: 300,
                currency: 'EUR',
                usdAmount: input.eurUsdRate ? 300 * input.eurUsdRate : 300,
                calculation: 'Minimum 300 EUR uygulandı'
            },
            transitFees
        };
        const portCallServices = pilotage.total +
            tugboat.total +
            mooring.total +
            quayDue.total +
            garbageCollection.usdAmount +
            sanitaryFee.usdAmount +
            lightDue.total +
            harbourMasterFee.usdAmount +
            chamberOfShipping.usdAmount +
            (freightShare?.usdAmount || 0) +
            agencyFee.usdAmount +
            (attendanceSupervisionFee?.usdAmount || 0);
        const transitServicesTotal = transitFees?.total || 0;
        const grandTotal = portCallServices + transitServicesTotal;
        return {
            input,
            roundedGRT,
            roundedNRT,
            fees,
            subtotals: {
                portCallServices,
                transitServices: transitServicesTotal > 0 ? transitServicesTotal : undefined
            },
            dangerousCargoSurcharge: isDangerous,
            grandTotal,
            currency: 'USD',
            calculationNotes
        };
    }
}
exports.CalculationService = CalculationService;
//# sourceMappingURL=service.js.map